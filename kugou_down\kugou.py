#coding=UTF-8
from hashlib import md5
import os
import re
import time
from urllib import parse
import requests
import json

class Kugou_music():
    def __init__(self,address="http://localhost:3001"):#http://localhost:3000，http://localhost:11301
        self.kugou = requests.session()
        self.headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                                      'AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36'}
        self.address = address
        try:
            with open('kugou.txt', 'r') as f:
                pass
            self.load_cookie()
        except:
            print("                         请先登陆")
            self.log_in()
        # print("当前账号",json.loads(self.netease.get(address+"/login/status").text)["data"]["profile"]["nickname"])
    def load_cookie(self):
        with open('kugou.txt') as f:
            cookie = json.loads(f.read())
            self.kugou.cookies.update(cookie)

    def save_cookie(self):
        with open('kugou.txt','w') as f:
            cookie=json.dumps(self.kugou.cookies.get_dict())
            f.write(cookie)
    
    def search_music(self,song_name):
        info=self.kugou.get(self.address+f"/search?keywords={song_name}&limit=1").text
        jsonData = json.loads(info)
        #判断歌曲不存在歌库的情况
        if not jsonData.get("result"):
            return 0,""
        info=jsonData['result']['songs'][0]
        id=info['id']
        name=info['name']
        return id,name

    def search_download_music(self,song_name,level="exhigh"):
        id,name=self.search_music(song_name=song_name)
        if not os.path.exists('input'):
            os.mkdir('input')
        song_url = self.kugou.get(self.address+f"/song/url/v1?id={id}&level={level}").text
        song_url=json.loads(song_url)['data'][0]['url']
        song=self.kugou.get(song_url)
        suffix = song_url.split(".")[-1]
        file_path='input\\' + name + '.' + suffix
        with open( file_path, 'wb') as f:
            f.write(song.content)
        return name,file_path

    def download_music(self,music_info,level="exhigh"):
        # id, _ = self.search_music(song_name=music_info)

        search_url = self.MD5Encrypt(music_info)
        search_text = self.get_html(search_url)
        hash_list = self.parse_text(search_text[12:-2])
        hash0 = hash_list[0]
        print(hash0)
        if not os.path.exists('input'):
            os.mkdir('input')#586567DAB042503A2D547E5038261927
        # 尝试获取歌曲URL，支持多个hash值
        song_url = None
        for i, hash_item in enumerate(hash_list[:5]):  # 尝试前5个hash值
            try:
                print(f"尝试获取歌曲URL (hash {i+1}/{min(5, len(hash_list))}): {hash_item[2]}")
                response = self.kugou.get(self.address+f"/song/url?hash={hash_item[2]}").text
                song_data = json.loads(response)

                # 检查是否有有效的URL
                if song_data.get('status') == 1:  # status=1表示成功
                    if 'backupUrl' in song_data and song_data['backupUrl']:
                        song_url = song_data['backupUrl'][0]
                        print(f"✅ 成功获取URL (使用backupUrl)")
                        break
                    elif 'url' in song_data and song_data['url']:
                        song_url = song_data['url'][0] if isinstance(song_data['url'], list) else song_data['url']
                        print(f"✅ 成功获取URL (使用url)")
                        break
                    elif 'data' in song_data and song_data['data'] and 'url' in song_data['data']:
                        song_url = song_data['data']['url']
                        print(f"✅ 成功获取URL (使用data.url)")
                        break
                else:
                    print(f"❌ hash {i+1} 无效 (status: {song_data.get('status', 'unknown')})")

            except Exception as e:
                print(f"❌ hash {i+1} 请求失败: {e}")
                continue

        if not song_url:
            raise Exception(f"无法获取到有效的音乐URL，已尝试 {min(5, len(hash_list))} 个hash值")
        song=self.kugou.get(song_url)
        # name=self.kugou.get(self.address+f"/song/detail?ids={id}").text
        # name=json.loads(name)['songs'][0]['name']
        name = music_info
        name = re.sub(r'[\[\]<>:"/\\|?*.]', '', name).rstrip('. ')  #特殊字符去除
        file_name='input\\' + name + '.mp3'
        with open( file_name, 'wb') as f:
            f.write(song.content)
        return name,file_name

    def MD5Encrypt(self, text):
        # 返回当前时间的时间戳(1970纪元后经过的浮点秒数)
        k = time.time()
        k = int(round(k * 1000))
        info = ["NVPh5oo715z5DIWAeQlhMDsWXXQV4hwt", "bitrate=0", "callback=callback123",
                "clienttime={}".format(k), "clientver=2000", "dfid=-", "inputtype=0",
                "iscorrection=1", "isfuzzy=0", "keyword={}".format(text), "mid={}".format(k),
                "page=1", "pagesize=30", "platform=WebFilter", "privilege_filter=0",
                "srcappid=2919", "tag=em", "userid=-1", "uuid={}".format(k), "NVPh5oo715z5DIWAeQlhMDsWXXQV4hwt"]
        # 创建md5对象
        new_md5 = md5()
        info = ''.join(info)
        # 更新哈希对象
        new_md5.update(info.encode(encoding='utf-8'))
        # 加密
        signature = new_md5.hexdigest()
        url = 'https://complexsearch.kugou.com/v2/search/song?callback=callback123&keyword={0}' \
              '&page=1&pagesize=30&bitrate=0&isfuzzy=0&tag=em&inputtype=0&platform=WebFilter&userid=-1' \
              '&clientver=2000&iscorrection=1&privilege_filter=0&srcappid=2919&clienttime={1}&' \
              'mid={2}&uuid={3}&dfid=-&signature={4}'.format(parse.quote(text), k, k, k, signature.upper())
        return url

    def get_html(self, url):
        try:
            response = requests.get(url, headers=self.headers, cookies=self.kugou.cookies.get_dict(), verify=False)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except Exception as err:
            print(err)
            return '请求异常'

    def parse_text(self, text):
        hash_list = []
        print('{:*^80}'.format('搜索结果如下'))
        print('{0:{5}<5}{1:{5}<15}{2:{5}<10}{3:{5}<10}{4:{5}<20}'.format('序号', '歌名', '歌手', '时长(s)', '专辑', chr(12288)))
        print('{:-^84}'.format('-'))
        song_list = json.loads(text)['data']['lists']
        for count, song in enumerate(song_list):
            singer_name = song['SingerName']
            # <em>本兮</em> 正则提取
            # 先匹配'</em>'这4中字符, 然后将其替换
            pattern = re.compile('[</em>]')
            singer_name = re.sub(pattern, '', singer_name)
            song_name = song['SongName']
            song_name = re.sub(pattern, '', song_name)
            album_name = song['AlbumName']
            album_id = song['AlbumID']
            # 时长
            duration = song['Duration']
            file_hash = song['FileHash']
            file_size = song['FileSize']

            # 音质为HQ, 高品质
            hq_file_hash = song['HQFileHash']
            hq_file_size = song['HQFileSize']

            # 音质为SQ, 超品质, 即无损, 后缀为flac
            sq_file_hash = song['SQFileHash']
            sq_file_size = song['SQFileSize']

            # MV m4a
            mv_hash = song['MvHash']
            m4a_size = song['M4aSize']

            hash_list.append([file_hash, hq_file_hash, sq_file_hash, album_id])

            print('{0:{5}<5}{1:{5}<15}{2:{5}<10}{3:{5}<10}{4:{5}<20}'.format(count, song_name, singer_name, duration, album_name,
                                                                             chr(12288)))
            # count += 1
            # if count == 10:
            #     # 为了测试方便, 这里只显示了10条数据
            #     break
        print('{:*^80}'.format('*'))
        return hash_list

    def log_in(self):
        # 略，已有cookie
        pass

    def test_download(self, test_songs=None):
        """测试下载功能"""
        if test_songs is None:
            test_songs = ["轻舟(dj阿卓版)", "戏说", "轻舟"]

        print(f"开始测试下载功能，共 {len(test_songs)} 首歌曲")
        success_count = 0

        for i, song in enumerate(test_songs, 1):
            print(f"\n{'='*50}")
            print(f"[{i}/{len(test_songs)}] 测试歌曲: {song}")
            print(f"{'='*50}")
            try:
                name, file_path = self.download_music(song)
                print(f"✅ 成功下载: {name} -> {file_path}")
                success_count += 1
            except Exception as e:
                print(f"❌ 下载失败: {e}")

        print(f"\n{'='*50}")
        print(f"测试完成: {success_count}/{len(test_songs)} 首歌曲下载成功")
        print(f"{'='*50}")
        return success_count == len(test_songs)

if __name__ == '__main__':
    kugou = Kugou_music()

    # 可以直接下载单首歌曲
    # kugou.download_music("轻舟(dj阿卓版)")

    # 或者运行测试
    kugou.test_download()