{"compilerOptions": {"target": "es5", "module": "commonjs", "experimentalDecorators": true, "moduleResolution": "node", "lib": ["esnext", "dom"], "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowJs": true, "sourceMap": true, "strict": true, "noImplicitAny": false, "noEmit": true, "baseUrl": "./", "resolveJsonModule": true, "rootDir": "./", "outDir": "./dist", "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["./interface.d.ts"]}