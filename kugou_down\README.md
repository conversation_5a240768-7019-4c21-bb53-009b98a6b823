# 酷狗音乐下载器

这是一个基于酷狗音乐API的音乐下载工具，支持搜索和下载酷狗音乐平台上的歌曲。

## 功能特点

- ✅ 支持歌曲搜索和下载
- ✅ 智能多hash值尝试，提高下载成功率
- ✅ 自动处理版权限制，寻找可用的音乐源
- ✅ 支持多种音乐格式（主要是MP3）
- ✅ 自动文件名清理，避免特殊字符问题

## 环境要求

- Python 3.6+
- requests库
- 酷狗音乐API服务（默认端口3001）

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 1. 启动酷狗音乐API服务

首先需要启动KuGouMusicApi-main文件夹下的API服务：

```bash
cd KuGouMusicApi-main
npm install
npm start
```

API服务默认运行在 http://localhost:3001

### 2. 运行下载器

```python
from kugou import Kugou_music

# 创建下载器实例
kugou = Kugou_music()

# 下载单首歌曲
name, file_path = kugou.download_music("轻舟(dj阿卓版)")
print(f"下载完成: {name} -> {file_path}")

# 运行测试（下载多首歌曲）
kugou.test_download(["轻舟(dj阿卓版)", "戏说", "轻舟"])
```

### 3. 直接运行脚本

```bash
cd kugou_down
python kugou.py
```

这将运行内置的测试，下载几首示例歌曲。

## 工作原理

1. **搜索歌曲**: 使用酷狗搜索API查找歌曲信息
2. **获取hash值**: 从搜索结果中提取多个hash值
3. **尝试获取URL**: 依次尝试每个hash值获取下载链接
4. **智能重试**: 如果某个hash值返回付费状态(status=2)，自动尝试下一个
5. **下载文件**: 成功获取URL后下载音乐文件到input文件夹

## 版权说明

- 本工具仅用于学习和研究目的
- 请尊重音乐版权，支持正版音乐
- 下载的音乐仅供个人学习使用，请勿用于商业用途

## 故障排除

### 常见问题

1. **API连接失败**
   - 确保KuGouMusicApi服务正在运行
   - 检查端口3001是否被占用

2. **下载失败**
   - 某些歌曲可能有版权保护
   - 尝试搜索其他版本或翻唱版本

3. **文件名错误**
   - 程序会自动清理特殊字符
   - 如有问题，检查歌曲名称是否包含特殊符号

## 更新日志

### v1.1 (当前版本)
- ✅ 修复了获取歌曲URL失败的问题
- ✅ 增加了多hash值智能重试机制
- ✅ 优化了错误处理和日志输出
- ✅ 添加了测试功能

### v1.0
- 基础的搜索和下载功能
